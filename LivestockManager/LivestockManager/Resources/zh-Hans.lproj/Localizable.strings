// General
"app.name" = "牧场管理";
"cancel" = "取消";
"save" = "保存";
"update" = "更新";
"edit" = "编辑";
"delete" = "删除";
"confirm" = "确认";
"done" = "完成";
"unknown" = "未知";
"required" = "必填";

// Tab & Navigation
"tab.home" = "首页";
"tab.add" = "添加";
"tab.profile" = "我的";
"tab.todo" = "代办";
"tab.search" = "搜索";
"tab.tools" = "工具箱";
"nav.myFarm" = "我的牧场";

// Todo
"todo.title" = "牧场代办";
"todo.add.placeholder" = "添加牧场代办事项...";
"todo.hide.completed" = "隐藏已完成";
"todo.show.completed" = "显示已完成";
"todo.empty" = "暂无代办事项";
"todo.empty.desc" = "添加一些牧场工作代办事项";
"todo.all.completed" = "所有任务已完成";
"todo.view.completed" = "显示已完成任务";
"todo.example.task1" = "检查牛棚围栏";
"todo.example.task2" = "给猪圈消毒";
"todo.example.task3" = "补充饲料";

// Home
"home.empty.welcome" = "欢迎来到您的牧场！";
"home.empty.noAnimals" = "您还没有添加任何动物呢";
"home.empty.startAdding" = "点击下方按钮，开始记录您的第一只动物吧";
"home.empty.addFirst" = "添加第一只动物";
"home.features.title" = "牧场管理功能";
"home.features.records" = "档案管理";
"home.features.records.desc" = "记录动物的详细信息，包括品种、年龄、健康状况等";
"home.features.search" = "快速搜索";
"home.features.search.desc" = "通过ID、品种等关键词快速找到您要查看的动物";
"home.features.filter" = "智能筛选";
"home.features.filter.desc" = "按物种、性别、状态等条件筛选，快速分类管理";
"home.features.stats" = "数据统计";
"home.features.stats.desc" = "实时查看存栏、待售、已售等状态的统计数据";
"home.tips.title" = "使用小贴士";
"home.tips.photos" = "建议为每只动物拍照，便于识别和管理";
"home.tips.ids" = "使用统一的编号规则，如\"牛001\"、\"羊001\"等";
"home.tips.updates" = "定期更新动物状态，保持数据准确性";
"home.tips.features" = "充分利用搜索和筛选功能提高管理效率";

// Filter
"filter.title" = "筛选动物";
"filter.applied" = "已应用筛选";
"filter.clear" = "清除";
"filter.clearAll" = "清除全部筛选";
"filter.apply" = "应用筛选";
"filter.reset" = "重置筛选";
"filter.noResults" = "没有符合筛选条件的动物";
"filter.noResults.help" = "请尝试调整筛选条件或清除全部筛选";
"filter.all" = "全部";
"filter.species" = "物种";
"filter.gender" = "性别";
"filter.status" = "状态";
"filter.age" = "年龄范围";
"filter.source" = "来源";
"filter.quick" = "快速筛选";
"filter.quick.healthy" = "健康动物";
"filter.quick.healthy.desc" = "（存栏状态）";
"filter.quick.forSale" = "可售动物";
"filter.quick.forSale.desc" = "（待售状态）";
"filter.quick.young" = "幼畜";
"filter.quick.young.desc" = "（1岁以下）";

// Animal Properties
"animal.id" = "唯一标识";
"animal.species" = "物种";
"animal.breed" = "品种";
"animal.gender" = "性别";
"animal.birthDate" = "出生日期";
"animal.age" = "年龄";
"animal.source" = "来源";
"animal.purchaseDate" = "购入日期";
"animal.purchasePrice" = "购入价格";
"animal.status" = "状态";
"animal.notes" = "备注";
"animal.photo" = "动物照片";
"animal.addPhoto" = "点击添加照片";

// Species
"species.cattle" = "牛";
"species.sheep" = "羊";
"species.pig" = "猪";
"species.horse" = "马";
"species.chicken" = "鸡";
"species.duck" = "鸭";
"species.goose" = "鹅";
"species.other" = "其他";

// Gender
"gender.male" = "公";
"gender.female" = "母";
"gender.castrated" = "阉割";

// Source
"source.selfBred" = "自繁";
"source.purchased" = "购入";

// Status
"status.active" = "存栏";
"status.forSale" = "待售";
"status.sold" = "已售";
"status.deceased" = "死亡";
"status.other" = "其他";

// Age Range
"ageRange.young" = "0-1岁";
"ageRange.middle" = "1-3岁";
"ageRange.adult" = "3岁以上";

// Add/Edit Animal
"add.title" = "添加动物";
"edit.title" = "编辑动物";
"add.id.hint" = "例如：耳标号、自定义编号";
"add.breed.placeholder" = "请输入品种";
"add.price.placeholder" = "请输入价格";
"add.notes.placeholder" = "请输入备注信息";
"add.requiredFields" = "* 为必填项";

// Detail View
"detail.basicInfo" = "基础信息";
"detail.statusInfo" = "状态信息";
"detail.notes" = "备注信息";
"detail.deleteConfirm" = "确认删除";
"detail.deleteMessage" = "删除后数据将无法恢复，是否确认删除？";

// Statistics
"stats.inStock" = "存栏";
"stats.forSale" = "待售";
"stats.sold" = "已售";
"stats.deceased" = "死亡";

// Search
"search.placeholder" = "搜索ID、品种...";
"search.noResults" = "未找到相关结果";
"search.recentSearches" = "最近搜索";
"search.clearHistory" = "清除历史";
"search.hotSearches" = "热门搜索";
"search.quickSearch" = "快捷搜索";

// Errors & Alerts
"error.required" = "请填写必填项";
"error.title" = "提示";

// Language Selection
"language.title" = "选择语言";
"language.close" = "关闭";
"language.changed" = "语言已更改";
"language.restart" = "请重启应用以完全应用语言更改";
"language.ok" = "确定";

// 个人资料
"profile.title" = "个人资料";
"profile.farm.management" = "牧场管理";
"profile.settings" = "设置";
"profile.help" = "帮助与反馈";
"profile.data.backup" = "数据备份";
"profile.data.export" = "数据导出";
"profile.statistics" = "统计报告";
"profile.notification" = "通知设置";
"profile.language" = "语言设置";
"profile.privacy" = "隐私政策";
"profile.about" = "版本信息";
"profile.version" = "版本 %@";
"profile.help.usage" = "使用帮助";
"profile.rate" = "评价我们";
"profile.contact" = "联系客服";
"profile.user.title" = "牧场主";
"profile.user.days" = "使用天数：%d天";
"profile.user.phone" = "电话：%@";
"profile.total.animals" = "动物总数";
"profile.animals.instock" = "存栏数量";
"profile.animals.added" = "本月新增";
"profile.edit" = "编辑资料";
"profile.back" = "返回";

// 通知设置
"notification.title" = "通知设置";
"notification.daily" = "每日提醒";
"notification.time" = "提醒时间";
"notification.system.settings" = "系统设置";
"notification.system.open" = "打开系统通知设置";
"notification.about" = "关于通知";
"notification.content" = "通知内容";
"notification.message" = "每日提醒将在设定时间通知您：\"是时候管理您的动物了！\"";
"notification.permission.denied" = "通知权限被拒绝";
"notification.permission.message" = "请在设备设置中允许此应用发送通知";
"notification.goto.settings" = "前往设置";
"notification.system.hint" = "如果您没有收到通知，请确保在系统设置中允许牧场管理发送通知";

// 电子邮件弹窗
"email.contact" = "联系客服";
"email.copy" = "复制邮箱";
"email.copied" = "已复制";
"email.close" = "关闭";

// 使用指南
"usage.guide.title" = "使用指南";
"usage.guide.close" = "关闭";
"usage.guide.content.1" = "1. 首页\n• 查看所有动物列表\n• 使用搜索快速找到特定动物\n• 使用筛选按条件查看动物";
"usage.guide.content.2" = "2. 添加动物\n• 点击底部的\"添加\"按钮\n• 填写动物基本信息\n• 可选择添加照片\n• 完成后点击保存";
"usage.guide.content.3" = "3. 动物管理\n• 点击动物卡片查看详情\n• 编辑或删除动物信息\n• 及时更新动物状态";
"usage.guide.content.4" = "4. 代办任务\n• 在\"代办\"页面添加工作提醒\n• 完成后标记为已完成\n• 可选择隐藏已完成任务";
"usage.guide.content.5" = "5. 通知提醒\n• 在设置中启用通知\n• 设置每日提醒时间\n• 不会错过重要工作";
"usage.guide.content.6" = "6. 数据安全\n• 定期检查数据准确性\n• 及时保存重要信息\n• 如有问题请联系客服";

// 工具页面
"tools.title" = "工具";
"tools.counter" = "计数器";
"tools.calculator" = "农场计算器";
"tools.weather" = "天气";
"tools.marketPrice" = "市场价格";
"tools.timer" = "定时器";
"tools.notes" = "记事本";
"tools.calendar" = "日历";
"tools.converter" = "单位转换";
"tools.medicine" = "药品记录";
"tools.basicTools" = "基础工具";
"tools.suggestions" = "用户建议";
"tools.suggestNewTool" = "建议新工具";

// 计数器工具
"tools.counter.start" = "开始新计数";
"tools.counter.reset" = "重置";
"tools.counter.complete" = "完成计数";
"tools.counter.counting" = "正在计数";
"tools.counter.startTime" = "开始时间: %@";
"tools.counter.new" = "开始一次新的计数";
"tools.counter.history" = "计数历史";
"tools.counter.save" = "保存计数";
"tools.counter.name" = "计数名称";
"tools.counter.note" = "备注";
"tools.counter.date" = "日期";
"tools.counter.count" = "计数";
"tools.counter.noHistory" = "暂无计数历史";
"tools.counter.deleteHistory" = "删除历史";

// Counter Tool Record
"tools.counter.record.title" = "记录详情";
"tools.counter.record.details" = "记录详情";
"tools.counter.record.count" = "计数";
"tools.counter.record.namePlaceholder" = "输入计数名称";
"tools.counter.record.notePlaceholder" = "输入备注";

// Counter Tool History
"tools.counter.history.title" = "计数历史";
"tools.counter.history.empty" = "暂无计数历史";
"tools.counter.history.countLabel" = "计数: %lld";
"tools.counter.history.timeLabel" = "时间: %@ - %@";
"tools.counter.history.noteLabel" = "备注: %@";

// 单位转换工具
"tools.converter.value" = "数值";
"tools.converter.from" = "从";
"tools.converter.to" = "到";
"tools.converter.result" = "转换结果";
"tools.converter.reference" = "常用换算参考";
"tools.converter.placeholder" = "输入数值";
"tools.converter.selectUnit" = "选择单位";

// 转换类别
"tools.converter.area" = "面积";
"tools.converter.weight" = "重量";
"tools.converter.volume" = "容量";
"tools.converter.length" = "长度";
"tools.converter.temperature" = "温度";

// 转换单位
"tools.converter.unit.squareMeter" = "平方米";
"tools.converter.unit.mu" = "亩";
"tools.converter.unit.hectare" = "公顷";
"tools.converter.unit.squareKilometer" = "平方公里";
"tools.converter.unit.acre" = "英亩";

"tools.converter.unit.kilogram" = "公斤";
"tools.converter.unit.gram" = "克";
"tools.converter.unit.jin" = "斤";
"tools.converter.unit.ton" = "吨";
"tools.converter.unit.pound" = "磅";

"tools.converter.unit.liter" = "升";
"tools.converter.unit.milliliter" = "毫升";
"tools.converter.unit.cubicMeter" = "立方米";
"tools.converter.unit.gallon" = "加仑";

"tools.converter.unit.meter" = "米";
"tools.converter.unit.centimeter" = "厘米";
"tools.converter.unit.kilometer" = "公里";
"tools.converter.unit.foot" = "英尺";
"tools.converter.unit.inch" = "英寸";

"tools.converter.unit.celsius" = "摄氏度";
"tools.converter.unit.fahrenheit" = "华氏度";
"tools.converter.unit.kelvin" = "开尔文";

// 转换器参考
"tools.converter.reference.muToSqM" = "1亩 ≈ 666.67平方米";
"tools.converter.reference.hectareToSqM" = "1公顷 = 10,000平方米";
"tools.converter.reference.acreToSqM" = "1英亩 ≈ 4,046.86平方米";
"tools.converter.reference.kgToJin" = "1千克 = 2斤";
"tools.converter.reference.tonToKg" = "1吨 = 1,000千克";
"tools.converter.reference.poundToKg" = "1磅 ≈ 0.453592千克";
"tools.converter.reference.literToMl" = "1升 = 1,000毫升";
"tools.converter.reference.gallonToLiter" = "1加仑(美制) ≈ 3.785升";
"tools.converter.reference.kmToM" = "1公里 = 1,000米";
"tools.converter.reference.footToM" = "1英尺 ≈ 0.3048米";
"tools.converter.reference.inchToCm" = "1英寸 = 2.54厘米";
"tools.converter.reference.celsiusToFahrenheit" = "0°C = 32°F (冰点)";
"tools.converter.reference.fahrenheitToCelsius" = "32°F = 0°C (冰点)";
"tools.converter.reference.celsiusToKelvin" = "0°C = 273.15K (绝对零度+273.15)";

// 天气工具
"tools.weather.locationPermissionTitle" = "需要位置权限";
"tools.weather.needsPermissionMessage" = "为了获取您所在位置的天气信息，我们需要访问您的位置信息。请在设置中允许访问位置信息。";
"tools.weather.farmingSuggestionTitle" = "农事建议";
"tools.weather.loading" = "正在加载天气信息...";
"tools.weather.errorUnknown" = "未知错误";
"tools.weather.errorLocation" = "无法获取位置信息";
"tools.weather.refresh" = "刷新";
"tools.weather.hourlyForecast" = "每小时预报";
"tools.weather.dailyForecast" = "未来天气";
"tools.weather.farmingSuggestions" = "农事建议";
"tools.weather.goToSettings" = "前往设置";
"tools.weather.needsPermission" = "需要位置权限才能获取您所在地区的天气";
"tools.weather.locationPermission" = "位置权限";
"tools.weather.cancel" = "取消";
"tools.weather.feelsLike" = "体感温度";
"tools.weather.humidity" = "湿度";
"tools.weather.wind" = "风速";
"tools.weather.uvIndex" = "紫外线";
"tools.weather.visibility" = "能见度";
"tools.weather.precipitation" = "降水量";
"tools.weather.today" = "今天";
"tools.weather.tomorrow" = "明天";
"tools.weather.noData" = "暂无天气数据";

// 天气工具 - 附加键值
"tools.weather.errorLocationUnavailable" = "位置信息不可用";
"tools.weather.errorManagerFailed" = "位置管理器错误";
"tools.weather.errorFetch" = "获取天气信息失败";
"tools.weather.gettingLocation" = "正在获取位置...";
"tools.weather.unknownLocation" = "未知位置";
"tools.weather.noSpecificSuggestionMessage" = "目前没有特别的农事建议";

// 天气工具 - UV指数
"tools.weather.uv.low" = "低";
"tools.weather.uv.moderate" = "中等";
"tools.weather.uv.high" = "高";
"tools.weather.uv.veryHigh" = "很高";
"tools.weather.uv.extreme" = "极高";

// 天气工具 - 农事建议
"tools.weather.suggestion.uvHigh" = "紫外线指数较高，建议采取防晒措施，注意防护动物";
"tools.weather.suggestion.heavyRainToday" = "今日有大雨，请做好动物防雨工作";
"tools.weather.suggestion.strongWindToday" = "今日风力较大，建议加固棚舍设施";
"tools.weather.suggestion.rainNext3Days" = "未来三天可能有雨，建议提前准备防雨措施";
"tools.weather.suggestion.heatWave" = "未来几天气温较高，注意防暑降温";
"tools.weather.suggestion.coldSpell" = "未来几天气温较低，注意保暖防寒";
"tools.weather.suggestion.irrigation" = "近期无雨，建议适当灌溉牧草";

// 农场计算器工具
"tools.calculator.feed" = "饲料计算器";
"tools.calculator.breeding" = "繁殖计算器";
"tools.calculator.cost" = "成本分析";
"tools.calculator.land" = "土地管理";
"tools.calculator.production" = "生产追踪";

// 农场计算器描述
"tools.calculator.feed.description" = "根据动物类型、年龄和体重计算每日饲料需求";
"tools.calculator.breeding.description" = "计算妊娠期和预产期";
"tools.calculator.cost.description" = "分析饲料、护理和销售成本";
"tools.calculator.land.description" = "计算肥料需求和播种率";
"tools.calculator.production.description" = "追踪产奶量和产蛋量";

// 饲料计算器
"tools.calculator.feed.title" = "饲料需求计算器";
"tools.calculator.animalType" = "动物类型";
"tools.calculator.animalAge" = "年龄(月)";
"tools.calculator.animalWeight" = "体重(公斤)";
"tools.calculator.animalCount" = "动物数量";
"tools.calculator.feedRequirement" = "每日饲料需求";
"tools.calculator.feedType" = "饲料类型";
"tools.calculator.calculate" = "计算";
"tools.calculator.result" = "结果";
"tools.calculator.feedingAdvice" = "饲喂建议";

// 繁殖计算器
"tools.calculator.breeding.title" = "繁殖计算器";
"tools.calculator.breeding.date" = "繁殖日期";
"tools.calculator.breeding.dueDate" = "预产期";
"tools.calculator.breeding.remainingDays" = "剩余天数";
"tools.calculator.breeding.gestationProgress" = "妊娠进度";
"tools.calculator.breeding.keyDates" = "关键日期";
"tools.calculator.breeding.progressCard" = "妊娠进度";
"tools.calculator.breeding.dueDateCard" = "预产期信息";
"tools.calculator.breeding.keyDatesCard" = "关键日期提醒";
"tools.calculator.breeding.managementTips" = "繁殖管理提示";

// 繁殖关键日期
"tools.calculator.breeding.breedingDateTitle" = "繁殖日期";
"tools.calculator.breeding.breedingDateDesc" = "动物交配或人工授精的日期";
"tools.calculator.breeding.pregnancyConfirmation" = "妊娠确认";
"tools.calculator.breeding.pregnancyConfirmationDesc" = "建议进行妊娠检查的时间";
"tools.calculator.breeding.dryPeriodStart" = "干奶期开始";
"tools.calculator.breeding.dryPeriodStartDesc" = "停止挤奶，让母牛为产犊做准备";
"tools.calculator.breeding.nestingPreparation" = "准备产床";
"tools.calculator.breeding.nestingPreparationDesc" = "为母猪准备产床和产仔环境";
"tools.calculator.breeding.vaccination" = "疫苗接种";
"tools.calculator.breeding.vaccinationDesc" = "为孕马接种必要的疫苗";
"tools.calculator.breeding.dueDateTitle" = "预产期";
"tools.calculator.breeding.dueDateDesc" = "预计分娩的日期";

// 繁殖建议
"tools.calculator.breeding.advice.cattle" = "牛的妊娠期约为283天（9.5个月）。在妊娠最后两个月需要特别注意营养补充，准备一个干净、宽敞的产犊区域。产前应停止挤奶（干奶期），让母牛的身体为产犊和新的泌乳周期做准备。";
"tools.calculator.breeding.advice.sheep" = "羊的妊娠期约为152天（5个月）。妊娠后期需要增加能量和蛋白质摄入。羊通常能够自己分娩，但应准备好干净的羊圈和必要的助产工具，以防需要协助。";
"tools.calculator.breeding.advice.pig" = "猪的妊娠期约为114天（3个月3周3天）。妊娠后期应确保母猪获得足够的能量和蛋白质。分娩前一周准备好干净、温暖的产床和保温灯，以确保新生仔猪的舒适。";
"tools.calculator.breeding.advice.horse" = "马的妊娠期约为340天（11个月）。妊娠马需要适当的运动和均衡的饮食。分娩前几周应密切观察马匹，准备干净的马厩，并确保兽医随时可以联系到，因为马的分娩过程需要特别关注。";
"tools.calculator.breeding.advice.goat" = "山羊的妊娠期约为150天（5个月）。妊娠后期需要增加能量摄入，减少压力。山羊通常能够自行分娩，但应准备好干净的羊圈和必要的助产工具，以便在需要时提供帮助。";
"tools.calculator.breeding.advice.default" = "请根据动物的具体品种和状况，咨询兽医获取专业的繁殖管理建议。在妊娠期间提供均衡的营养，减少应激，并为即将到来的分娩做好准备。";

// 计算器单位
"tools.calculator.unit.days" = "天";
"tools.calculator.unit.months" = "月";
"tools.calculator.unit.kg" = "公斤";
"tools.calculator.unit.weekly" = "每周需求:";
"tools.calculator.unit.monthly" = "每月需求:";
"tools.calculator.singleAnimal" = "单个动物:";
"tools.calculator.allAnimals" = "全部动物:";

// 动物类型
"tools.calculator.animal.cattle" = "牛";
"tools.calculator.animal.sheep" = "羊";
"tools.calculator.animal.pig" = "猪";
"tools.calculator.animal.chicken" = "鸡";
"tools.calculator.animal.duck" = "鸭";
"tools.calculator.animal.horse" = "马";
"tools.calculator.animal.goat" = "山羊";

// 饲料类型
"tools.calculator.feed.grain" = "谷物";
"tools.calculator.feed.hay" = "干草";
"tools.calculator.feed.silage" = "青贮饲料";
"tools.calculator.feed.concentrate" = "浓缩饲料";

// 成本分析器
"tools.calculator.cost.title" = "成本分析器";
"tools.calculator.cost.raisingPeriod" = "饲养周期(月)";
"tools.calculator.cost.monthlyCostEstimate" = "月度成本估计（元/月）";
"tools.calculator.cost.feedCost" = "饲料成本";
"tools.calculator.cost.vetCost" = "兽医成本";
"tools.calculator.cost.laborCost" = "劳动力成本";
"tools.calculator.cost.otherCost" = "其他成本";
"tools.calculator.cost.expectedSalePrice" = "预期售价（元/头）";
"tools.calculator.cost.totalCost" = "总成本";
"tools.calculator.cost.totalRevenue" = "总收入";
"tools.calculator.cost.profit" = "利润";
"tools.calculator.cost.roi" = "投资回报率";
"tools.calculator.cost.costBreakdown" = "成本明细";
"tools.calculator.cost.profitAnalysis" = "盈利分析";
"tools.calculator.cost.monthlyFeedCost" = "每月饲料费用";
"tools.calculator.cost.monthlyVetCost" = "每月兽医费用";
"tools.calculator.cost.monthlyLaborCost" = "每月劳动力费用";
"tools.calculator.cost.monthlyOtherCost" = "每月其他费用";

// 土地管理
"tools.calculator.land.title" = "土地管理计算器";
"tools.calculator.land.description" = "计算每单位面积的资源使用量";
"tools.calculator.land.area" = "土地面积";
"tools.calculator.land.resourceType" = "资源类型";
"tools.calculator.land.fertilizer" = "肥料";
"tools.calculator.land.seed" = "种子";
"tools.calculator.land.amount" = "总用量";
"tools.calculator.land.perUnitArea" = "每单位面积用量";
"tools.calculator.land.fertilizerType" = "肥料类型";
"tools.calculator.land.seedType" = "种子类型";
"tools.calculator.land.compound" = "复合肥";
"tools.calculator.land.organic" = "有机肥";
"tools.calculator.land.nitrogen" = "氮肥";
"tools.calculator.land.phosphorus" = "磷肥";
"tools.calculator.land.potassium" = "钾肥";
"tools.calculator.land.corn" = "玉米";
"tools.calculator.land.wheat" = "小麦";
"tools.calculator.land.rice" = "水稻";
"tools.calculator.land.soybean" = "大豆";
"tools.calculator.land.grass" = "牧草";

// 生产追踪
"tools.calculator.production.title" = "生产追踪计算器";
"tools.calculator.production.description" = "追踪动物的产奶量或产蛋量";
"tools.calculator.production.animalType" = "动物类型";
"tools.calculator.production.animalCount" = "动物数量";
"tools.calculator.production.productionAmount" = "生产量";
"tools.calculator.production.date" = "日期";
"tools.calculator.production.addRecord" = "添加记录";
"tools.calculator.production.records" = "生产记录";
"tools.calculator.production.averagePerAnimal" = "平均每只";
"tools.calculator.production.totalProduction" = "总产量";
"tools.calculator.production.expectedRange" = "预期范围";
"tools.calculator.production.cow" = "奶牛";
"tools.calculator.production.goat" = "奶山羊";
"tools.calculator.production.sheep" = "奶绵羊";
"tools.calculator.production.chicken" = "蛋鸡";
"tools.calculator.production.milk" = "奶";
"tools.calculator.production.egg" = "蛋";
"tools.calculator.production.liter" = "升";
"tools.calculator.production.piece" = "枚";
"tools.calculator.production.noRecords" = "暂无生产记录";

// 饲喂建议文本
"tools.calculator.advice.cattle.young" = "幼龄牛需要高蛋白饲料，建议提供优质的初生牛犊饲料。每天分3-4次喂食，确保有足够的清水。";
"tools.calculator.advice.cattle.growing" = "生长期牛需要均衡的饮食，包括足够的蛋白质和能量。建议饲喂优质干草、粗饲料和适量精饲料。";
"tools.calculator.advice.cattle.adult" = "成年牛可以主要以粗饲料为主，根据生产状态补充精饲料。确保饲料新鲜，饮水充足。";
"tools.calculator.advice.sheep" = "羊需要优质粗饲料，特别是好的干草。如果放牧，确保充足的草场面积。补充矿物质，特别是铜和硒。";
"tools.calculator.advice.pig.young" = "仔猪需要高质量的初生料，富含蛋白质和必需氨基酸。每天多次少量喂食。";
"tools.calculator.advice.pig.adult" = "生长育肥猪需要平衡的日粮，包括谷物、蛋白质补充剂和矿物质。控制喂食量以防止过度肥胖。";
"tools.calculator.advice.chicken" = "鸡需要全价配合饲料，包含适当水平的蛋白质、能量、维生素和矿物质。蛋鸡需要额外的钙质。";
"tools.calculator.advice.duck" = "鸭子饲料应含有足够的蛋白质和能量。如有条件，可提供水生环境和部分绿色饲料。";
"tools.calculator.advice.horse" = "马以优质干草为主要饲料，辅以适量的谷物。饲喂次数应多，每次量少。提供盐块和矿物质补充剂。";
"tools.calculator.advice.default" = "请根据动物种类和生长阶段提供均衡的饮食，确保充足的饮水。";